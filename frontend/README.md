<h2 align="center">
中证数智-企业知识库管理平台
</h2>
<img width="1041" alt="中证数智企业知识库管理平台" src="https://github.com/user-attachments/assets/b6ee6a78-5d37-496d-ae10-ce18eee7a1d6">
<h3 align="center">
企业级智能知识库管理平台 - 高效管理和检索企业知识资产
</h3>

# 关于项目

中证数智-企业知识库管理平台是一个基于 React+Next.js 的企业级智能知识库管理系统，旨在为企业提供一个高效、易用的知识管理和检索平台。该系统通过先进的AI技术，帮助企业更好地组织、管理和利用知识资产，提升工作效率和决策质量。

## 核心功能

- **🗂️ 文档管理**: 上传、更新和删除文档及其元数据
- **🛝 对话体验**: 支持多种模型的流式对话响应和可配置设置
- **📊 数据分析**: 查看延迟和指标的聚合统计信息及详细直方图
- **📜 日志记录**: 跟踪用户查询、搜索结果和AI响应
- **🔧 开发工具**: 轻松启动开发服务器、格式化代码和运行检查


# 快速安装

### Install PNPM

PNPM is a fast, disk space-efficient package manager that helps you manage your project dependencies. To install PNPM, visit the [official PNPM installation page](https://pnpm.io/installation) for the latest instructions, or follow the instructions outlined below:

<details>
<summary>PNPM Installation</summary>

For Unix-based systems (Linux, macOS):

```bash
curl -fsSL https://get.pnpm.io/install.sh | sh -
```

For Windows:

```powershell
iwr https://get.pnpm.io/install.ps1 -useb | iex
```

After installing PNPM, you may need to add it to your system's PATH. Follow the instructions provided on the PNPM installation page to ensure it's properly set up.

</details>

1. **Install the project dependencies using PNPM:**

   ```bash
   pnpm install
   ```

2. **Build and start the application for production:**

   ```bash
   pnpm build
   pnpm start
   ```

This will build the application on port 3000. After `pnpm start` runs successfully, the dashboard can be viewed at [http://localhost:3000](http://localhost:3000).

### Developing with the Dashboard

If you'd like to develop the dashboard, you can do so by starting a development server:

1. **Start the development server:**

   ```bash
   pnpm dev
   ```

2. **Pre-commit checks (optional but recommended):**

   Ensure your code is properly formatted and free of linting issues before committing:

   ```bash
   pnpm format
   pnpm lint
   ```
