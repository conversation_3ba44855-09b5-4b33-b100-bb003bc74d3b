{"name": "citic-securities-knowledge-platform", "version": "1.0.4", "private": true, "scripts": {"dev": "next dev -p 3005", "build": "next build", "start": "next start", "lint": "eslint --fix .", "lint:verbose": "eslint --config .eslintrc.verbose.json .", "format": "prettier --write .", "format:check": "prettier --check ."}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}, "license": "MIT", "dependencies": {"@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.5", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-tooltip": "^1.1.7", "@react-spring/web": "^9.7.5", "@sentry/nextjs": "^9", "@supabase/supabase-js": "^2.48.1", "@tailwindcss/forms": "^0.5.10", "@types/topojson-specification": "^1.0.5", "@visx/axis": "^3.12.0", "@visx/curve": "^3.12.0", "@visx/event": "^3.12.0", "@visx/geo": "^3.12.0", "@visx/gradient": "^3.12.0", "@visx/grid": "^3.12.0", "@visx/group": "^3.12.0", "@visx/pattern": "^3.12.0", "@visx/scale": "^3.12.0", "@visx/shape": "^3.12.0", "@visx/stats": "^3.12.0", "@visx/tooltip": "^3.12.0", "@visx/vendor": "^3.12.0", "ansi_up": "^6.0.2", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3": "^7.9.0", "d3-array": "^3.2.4", "date-fns": "^3.6.0", "embla-carousel-react": "^8.5.2", "framer-motion": "^11.18.2", "lucide-react": "^0.395.0", "next": "14.2.5", "next-themes": "^0.3.0", "pnpm": "^10.14.0", "posthog-js": "^1.212.1", "r2r-js": "^0.4.34", "react": "18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "18.3.1", "react-dropzone": "^14.3.5", "react-markdown": "^9.0.3", "sass": "^1.83.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "topojson-client": "^3.1.0", "uuid": "^10.0.0"}, "devDependencies": {"@types/d3": "^7.4.3", "@types/d3-array": "^3.2.1", "@types/react": "18.3.3", "@types/topojson-client": "^3.1.5", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "14.2.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "husky": "^9.1.7", "lint-staged": "^15.4.3", "prettier": "^3.4.2", "tailwindcss": "^3.4.17", "typescript": "5.5.2"}}