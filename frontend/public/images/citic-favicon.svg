<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="faviconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stopColor="#B91C1C" />
      <stop offset="100%" stopColor="#EF4444" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆 -->
  <circle cx="16" cy="16" r="15" fill="url(#faviconGradient)"/>
  
  <!-- 简化的"中"字标识 -->
  <g fill="white">
    <rect x="14" y="8" width="4" height="16" rx="1"/>
    <rect x="8" y="14" width="16" height="4" rx="1"/>
  </g>
  
  <!-- 四个角的数据点 -->
  <g fill="white" opacity="0.8">
    <circle cx="10" cy="10" r="1.5"/>
    <circle cx="22" cy="10" r="1.5"/>
    <circle cx="10" cy="22" r="1.5"/>
    <circle cx="22" cy="22" r="1.5"/>
  </g>
</svg>
