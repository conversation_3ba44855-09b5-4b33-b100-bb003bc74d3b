<svg width="256" height="256" viewBox="0 0 256 256" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="citicGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stopColor="#B91C1C" />
      <stop offset="50%" stopColor="#DC2626" />
      <stop offset="100%" stopColor="#EF4444" />
    </linearGradient>
  </defs>
  
  <!-- 外圆环 -->
  <circle cx="128" cy="128" r="120" fill="none" stroke="url(#citicGradient)" stroke-width="8"/>
  
  <!-- 中证数智标识 - 抽象的数据和智能符号 -->
  <g fill="url(#citicGradient)">
    <!-- 中心的"中"字形状 -->
    <rect x="118" y="80" width="20" height="96" rx="2"/>
    <rect x="80" y="118" width="96" height="20" rx="2"/>
    
    <!-- 数据节点 -->
    <circle cx="80" cy="80" r="8"/>
    <circle cx="176" cy="80" r="8"/>
    <circle cx="80" cy="176" r="8"/>
    <circle cx="176" cy="176" r="8"/>
    
    <!-- 连接线表示智能网络 -->
    <path d="M88 88 L118 118" stroke="url(#citicGradient)" stroke-width="3" fill="none"/>
    <path d="M168 88 L138 118" stroke="url(#citicGradient)" stroke-width="3" fill="none"/>
    <path d="M88 168 L118 138" stroke="url(#citicGradient)" stroke-width="3" fill="none"/>
    <path d="M168 168 L138 138" stroke="url(#citicGradient)" stroke-width="3" fill="none"/>
  </g>
  
  <!-- 文字标识 -->
  <text x="128" y="220" text-anchor="middle" fill="url(#citicGradient)" font-family="Arial, sans-serif" font-size="16" font-weight="bold">中证数智</text>
</svg>
