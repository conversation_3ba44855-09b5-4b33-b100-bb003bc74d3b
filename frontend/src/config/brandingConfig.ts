// src/config/brandingConfig.ts

import brandingOverride from './brandingOverride';

// Define default branding configuration
const defaultConfig = {
  companyName: '中证数智科技有限公司',
  deploymentName: '中证数智-企业知识库管理平台',
  socialLinks: {
    twitter: { enabled: false, url: '' },
    github: { enabled: false, url: '' },
    discord: { enabled: false, url: '' },
  },
  navbar: {
    appName: '中证数智-企业知识库管理平台',
    showDocsButton: true,
    menuItems: {
      home: true,
      documents: true,
      collections: true,
      chat: true,
      search: true,
      users: true,
      logs: true,
      analytics: false,
      settings: true,
    },
  },
  logo: {
    src: '/images/logo.png',
    alt: '中证数智企业知识库管理平台',
  },
  theme: 'dark',
  homePage: {
    pythonSdk: false,
    githubCard: false,
    hatchetCard: false,
  },
  nextConfig: {
    additionalRemoteDomain: '',
  },
};

// ✅ Declare `window.__BRANDING_CONFIG__` globally to avoid TypeScript errors
declare global {
  interface Window {
    __BRANDING_CONFIG__?: Partial<typeof defaultConfig>;
  }
}

// ✅ Load user-defined config from `window.__BRANDING_CONFIG__` (if available)
const userConfig =
  (typeof window !== 'undefined' && window.__BRANDING_CONFIG__) || {};

// ✅ Merge `defaultConfig`, `brandingOverride.ts`, and `userConfig`
export const brandingConfig = {
  ...defaultConfig,
  ...brandingOverride,
  ...userConfig,
};
