// src/config/brandingOverride.ts

// This file allows overriding branding configurations.
// If no override is provided, defaults from `brandingConfig.ts` will be used.

const brandingOverride = {
  // 中证数智企业知识库管理平台配置覆盖
  companyName: '中证数智科技有限公司',
  deploymentName: '中证数智-企业知识库管理平台',
  navbar: {
    appName: '中证数智-企业知识库管理平台',
    showDocsButton: true,
    menuItems: {
      home: true,
      documents: true,
      collections: true,
      chat: true,
      search: true,
      users: true,
      logs: true,
      analytics: false,
      settings: true,
    },
  },
  logo: {
    src: '/images/logo.png',
    alt: '中证数智企业知识库管理平台',
  },
  theme: 'dark',
  homePage: {
    pythonSdk: false, // 禁用Python SDK卡片
    githubCard: false, // 禁用GitHub卡片
    hatchetCard: false, // 禁用Hatchet卡片
  },
  socialLinks: {
    twitter: { enabled: false, url: '' },
    github: { enabled: false, url: '' },
    discord: { enabled: false, url: '' },
  },
};

// Export the override object
export default brandingOverride;
