import React, { useState } from 'react';

import { Button } from '@/components/ui/Button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { useUserContext } from '@/context/UserContext';

interface CollectionCreationModalProps {
  open: boolean;
  onClose: () => void;
  onCollectionCreated: () => void;
}

const CollectionCreationModal: React.FC<CollectionCreationModalProps> = ({
  open,
  onClose,
  onCollectionCreated,
}) => {
  const { getClient } = useUserContext();
  const { toast } = useToast();

  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  const handleCreate = async () => {
    if (!name.trim()) {
      toast({
        title: '验证错误',
        description: '集合名称是必填项。',
        variant: 'destructive',
      });
      return;
    }

    setIsCreating(true);
    try {
      const client = await getClient();
      if (!client) {
        throw new Error('获取认证客户端失败');
      }

      await client.collections.create({
        name: name.trim(),
        description: description.trim() || undefined,
      });
      toast({
        title: '集合已创建',
        description: `集合 "${name}" 已成功创建。`,
        variant: 'success',
      });
      setName('');
      setDescription('');
      onClose();
      onCollectionCreated();
    } catch (error: any) {
      console.error('Error creating collection:', error);
      toast({
        title: '错误',
        description:
          error?.message ||
          '创建集合时发生意外错误。',
        variant: 'destructive',
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleClose = () => {
    if (!isCreating) {
      setName('');
      setDescription('');
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-2xl">创建新集合</DialogTitle>
        </DialogHeader>
        <div className="mt-4">
          <label className="block mb-2 font-medium">
            集合名称<span className="text-red-500">*</span>
          </label>
          <Input
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="请输入集合名称"
            required
          />
        </div>
        <div className="mt-4">
          <label className="block mb-2 font-medium">描述</label>
          <Textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="请输入集合描述（可选）"
          />
        </div>
        <DialogFooter className="mt-6">
          <Button color="filled" onClick={handleClose} disabled={isCreating}>
            取消
          </Button>
          <Button color="filled" onClick={handleCreate} disabled={isCreating}>
            {isCreating ? '创建中...' : '创建'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CollectionCreationModal;
