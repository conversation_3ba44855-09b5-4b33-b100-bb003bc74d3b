import React from 'react';

interface ModernLogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const ModernLogo: React.FC<ModernLogoProps> = ({ 
  className = '', 
  size = 'md' 
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  };

  return (
    <div className={`${sizeClasses[size]} ${className} relative`}>
      <svg
        viewBox="0 0 100 100"
        className="w-full h-full"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#B91C1C" />
            <stop offset="50%" stopColor="#DC2626" />
            <stop offset="100%" stopColor="#EF4444" />
          </linearGradient>
          <filter id="glow">
            <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        {/* 外圆环 */}
        <circle
          cx="50"
          cy="50"
          r="45"
          fill="none"
          stroke="url(#logoGradient)"
          strokeWidth="3"
          className="animate-pulse"
        />

        {/* 中证数智标识 - 知识库和数据符号 */}
        <g filter="url(#glow)">
          {/* 中心的"中"字形状 */}
          <rect
            x="47"
            y="30"
            width="6"
            height="40"
            rx="1"
            fill="url(#logoGradient)"
            className="animate-pulse"
          />
          <rect
            x="30"
            y="47"
            width="40"
            height="6"
            rx="1"
            fill="url(#logoGradient)"
            className="animate-pulse"
          />

          {/* 数据节点 */}
          <circle cx="35" cy="35" r="3" fill="url(#logoGradient)" opacity="0.9" />
          <circle cx="65" cy="35" r="3" fill="url(#logoGradient)" opacity="0.9" />
          <circle cx="35" cy="65" r="3" fill="url(#logoGradient)" opacity="0.9" />
          <circle cx="65" cy="65" r="3" fill="url(#logoGradient)" opacity="0.9" />

          {/* 连接线表示智能网络 */}
          <path d="M38 38 L47 47" stroke="url(#logoGradient)" strokeWidth="2" fill="none" opacity="0.7"/>
          <path d="M62 38 L53 47" stroke="url(#logoGradient)" strokeWidth="2" fill="none" opacity="0.7"/>
          <path d="M38 62 L47 53" stroke="url(#logoGradient)" strokeWidth="2" fill="none" opacity="0.7"/>
          <path d="M62 62 L53 53" stroke="url(#logoGradient)" strokeWidth="2" fill="none" opacity="0.7"/>
        </g>
      </svg>
    </div>
  );
};

export default ModernLogo;
