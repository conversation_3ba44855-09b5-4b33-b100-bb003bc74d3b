import { SquarePen, ChevronDown, ChevronUp } from 'lucide-react';
import { WrappedSettingsResponse } from 'r2r-js';
import React, { useState, useEffect, useCallback } from 'react';

import EditPromptDialog from '@/components/ChatDemo/utils/editPromptDialog';
import Layout from '@/components/Layout';
import { Button } from '@/components/ui/Button';
import { useUserContext } from '@/context/UserContext';

interface Prompt {
  name: string;
  template: string;
}

interface AppData {
  config: Record<string, any>;
  prompts: Prompt[];
}

// 配置项键名翻译映射
const getConfigKeyTranslation = (key: string): string => {
  const translations: Record<string, string> = {
    // Agent 相关配置
    'knowledgeBase': '知识库',
    'true': '启用',
    'false': '禁用',
    'ragAgentGenerationConfig': 'RAG代理生成配置',
    'dynamic_rag_agent': '动态RAG代理',
    'ragTools': 'RAG工具',
    'research-tools': '研究工具',
    'stream': '流式传输',
    'maintenances': '维护',
    'ragAgentGenerationConfig': 'RAG代理生成配置',
    'researchTools': '研究工具',

    // App 相关配置
    'authUrl': '认证URL',
    'openai/gpt-4o-mini': 'OpenAI GPT-4o Mini',
    'defaultMaxChunkSize': '默认最大块大小',
    'defaultMaxChunksPerDoc': '默认每文档最大块数',
    'defaultCollectionChunkSize': '默认集合块大小',
    'hostUrl': '主机URL',
    'maxUploadSizeType': '最大上传大小类型',
    'defaultMaxChunkOverlap': '默认最大块重叠',
    'defaultMaxChunksPerDoc': '默认每文档最大块数',
    'defaultCollectionChunkSize': '默认集合块大小',
    'hostUrl': '主机URL',

    // 文件类型配置
    'bmp': 'BMP图片',
    'csv': 'CSV文件',
    'doc': 'Word文档',
    'docx': 'Word文档(新版)',
    'gif': 'GIF图片',
    'html': 'HTML文件',
    'jpeg': 'JPEG图片',
    'jpg': 'JPG图片',
    'json': 'JSON文件',
    'md': 'Markdown文件',
    'pdf': 'PDF文件',
    'png': 'PNG图片',
    'ppt': 'PowerPoint演示文稿',
    'pptx': 'PowerPoint演示文稿(新版)',
    'svg': 'SVG矢量图',
    'txt': '文本文件',
    'xls': 'Excel表格',
    'xlsx': 'Excel表格(新版)',
    'xml': 'XML文件',
    'msg': 'Outlook邮件',

    // 其他常见配置项
    'extraFields': '额外字段',
    'extraParsers': '额外解析器',
    'parserOverrides': '解析器覆盖',
    'routeLimits': '路由限制',
    'userLimits': '用户限制',
    'temperature': '温度',
    'topP': 'Top P',
    'top_k': 'Top K',
    'maxTokensToSample': '最大采样令牌数',
    'model': '模型',
    'provider': '提供商',
    'stream': '流式传输',
    'use_semantic_search': '使用语义搜索',
    'use_hybrid_search': '使用混合搜索',
    'use_kg_search': '使用知识图谱搜索',
    'kg_search_type': '知识图谱搜索类型',
    'kg_search_level': '知识图谱搜索级别',
    'generation_config': '生成配置',
    'kg_agent_generation_config': '知识图谱代理生成配置',
    'search_config': '搜索配置',
    'completion_config': '完成配置',
    'embedding_config': '嵌入配置',
    'kg_config': '知识图谱配置',
    'logging_config': '日志配置',
    'prompt_config': '提示词配置',
    'retrieval_config': '检索配置',
    'ingestion_config': '摄取配置',
    'auth_config': '认证配置',
    'database_config': '数据库配置',
    'vector_database_config': '向量数据库配置',
    'kg_database_config': '知识图谱数据库配置',
    'app_config': '应用配置',
    'server_config': '服务器配置',
    'client_config': '客户端配置',
    'api_config': 'API配置',
    'ui_config': 'UI配置',
    'feature_flags': '功能标志',
    'experimental_features': '实验性功能',
    'debug_mode': '调试模式',
    'verbose_logging': '详细日志',
    'cache_config': '缓存配置',
    'security_config': '安全配置',
    'performance_config': '性能配置',
    'monitoring_config': '监控配置',
    'backup_config': '备份配置',
    'maintenance_config': '维护配置',

    // 从截图中看到的具体配置项
    'Agent': '代理',
    'App': '应用',
    'bmp': 'BMP图片',
    'csv': 'CSV文件',
    'doc': 'Word文档',
    'docx': 'Word文档(新版)',
    'gif': 'GIF图片',
    'html': 'HTML文件',
    'jpeg': 'JPEG图片',
    'jpg': 'JPG图片',
    'json': 'JSON文件',
    'md': 'Markdown文件',
    'pdf': 'PDF文件',
    'png': 'PNG图片',
    'ppt': 'PowerPoint演示文稿',
    'pptx': 'PowerPoint演示文稿(新版)',
    'svg': 'SVG矢量图',
    'txt': '文本文件',
    'xls': 'Excel表格',
    'xlsx': 'Excel表格(新版)',
    'xml': 'XML文件',
    'msg': 'Outlook邮件'
  };

  return translations[key] || key;
};

// 配置值翻译函数
const getConfigValueTranslation = (value: any): string => {
  if (typeof value === 'boolean') {
    return value ? '启用' : '禁用';
  }

  if (typeof value === 'number') {
    // 为大数字添加格式化
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return String(value);
  }

  if (typeof value === 'string') {
    const valueTranslations: Record<string, string> = {
      'true': '启用',
      'false': '禁用',
      'enabled': '启用',
      'disabled': '禁用',
      'yes': '是',
      'no': '否',
      'on': '开启',
      'off': '关闭',
      'local': '本地',
      'remote': '远程',
      'auto': '自动',
      'manual': '手动',
      'high': '高',
      'medium': '中',
      'low': '低',
      'fast': '快速',
      'slow': '慢速',
      'normal': '正常',
      'debug': '调试',
      'info': '信息',
      'warn': '警告',
      'error': '错误',
      'openai/gpt-4o-mini': 'OpenAI GPT-4o Mini',
      'openai/gpt-4': 'OpenAI GPT-4',
      'openai/gpt-3.5-turbo': 'OpenAI GPT-3.5 Turbo',
      '/openai/vectorizing/response': '/openai/向量化/响应',
      'openai/vectorizing/response': 'OpenAI向量化响应'
    };

    return valueTranslations[value] || value;
  }

  return String(value);
};

const renderNestedConfig = (
  config: Record<string, any>,
  depth = 0
): JSX.Element => {
  // Update getFontSize function to handle both key and value lengths
  const getFontSize = (content: string, isKey: boolean = false): string => {
    const length = content.length;
    if (isKey) {
      if (length > 30) return 'text-xs';
      if (length > 20) return 'text-sm';
      return 'text-base';
    }
    // existing value length logic
    if (length > 100) return 'text-xs';
    if (length > 50) return 'text-sm';
    return 'text-base';
  };

  if (typeof config !== 'object' || config === null) {
    const valueStr =
      typeof config === 'string' ? `"${getConfigValueTranslation(config)}"` : getConfigValueTranslation(config);
    return (
      <div className="bg-zinc-700 rounded p-3 ml-6 text-gray-300">
        <span className="text-accent-base font-normal">值: </span>
        <span
          className={`whitespace-pre-wrap font-normal ${getFontSize(valueStr)}`}
        >
          {valueStr}
        </span>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 auto-rows-auto">
      {Object.entries(config)
        .sort(([keyA], [keyB]) => {
          // Always put extraFields last
          if (keyA === 'extraFields') return 1;
          if (keyB === 'extraFields') return -1;

          // Sort other keys normally
          const groupOrder: Record<string, number> = {
            extraParsers: 0,
            parserOverrides: 1,
          };
          if (keyA in groupOrder && keyB in groupOrder)
            return groupOrder[keyA] - groupOrder[keyB];
          if (keyA in groupOrder) return -1;
          if (keyB in groupOrder) return 1;
          return keyA.localeCompare(keyB);
        })
        .map(([key, value]) => {
          const contentLength = JSON.stringify(value).length;
          const isComplexObject =
            typeof value === 'object' &&
            value !== null &&
            !Array.isArray(value);

          // Special handling for grouped and compact keys
          const isGroupedKey = [
            'extraFields',
            'extraParsers',
            'parserOverrides',
          ].includes(key);
          const isSpecialKey = ['routeLimits', 'userLimits'].includes(key);
          const needsCompactLayout = key === 'parserOverrides' || isSpecialKey;

          // Modify spanning logic
          const shouldSpanTwo =
            !isSpecialKey &&
            !isGroupedKey &&
            contentLength > 100 &&
            !isComplexObject;
          const shouldSpanThree =
            !isSpecialKey &&
            !isGroupedKey &&
            (isComplexObject || contentLength > 300);

          const spanClass = shouldSpanThree
            ? 'md:col-span-2 lg:col-span-3'
            : shouldSpanTwo
              ? 'md:col-span-2'
              : needsCompactLayout
                ? 'col-span-1 max-w-[300px]' // Add max-width for compact containers
                : isGroupedKey
                  ? 'col-span-1'
                  : '';

          // Use compact container for special cases
          const containerClass = needsCompactLayout
            ? 'bg-zinc-700 rounded p-3 h-fit'
            : isGroupedKey
              ? 'bg-zinc-700 rounded p-3'
              : 'bg-zinc-700 rounded p-4';

          // For extraFields, format without curly brackets
          const valueStr =
            key === 'extraFields' && typeof value === 'object'
              ? JSON.stringify(value, null, 2)
                  .replace(/^{/, '') // Remove opening curly brace
                  .replace(/}$/, '') // Remove closing curly brace
                  .trim()
              : typeof value === 'string'
                ? `"${getConfigValueTranslation(value)}"`
                : Array.isArray(value)
                  ? `[${value.map((item) => (typeof item === 'string' ? `"${getConfigValueTranslation(item)}"` : JSON.stringify(item))).join(', ')}]`
                  : getConfigValueTranslation(value);

          return (
            <div key={key} className={`${containerClass} ${spanClass}`}>
              <div
                className="text-white"
                style={{ paddingLeft: `${depth * 16}px` }}
              >
                <span className="text-accent-base font-normal">键: </span>
                <span className={`font-normal ${getFontSize(key, true)}`}>
                  {getConfigKeyTranslation(key)}
                </span>
                <br />
                {typeof value === 'object' &&
                value !== null &&
                key !== 'extraFields' ? (
                  Array.isArray(value) ? (
                    <span
                      className={`whitespace-pre-wrap font-normal ${getFontSize(valueStr)}`}
                    >
                      <span className="text-accent-base">值: </span>
                      {valueStr}
                    </span>
                  ) : (
                    <div className="mt-2">
                      {renderNestedConfig(value, depth + 1)}
                    </div>
                  )
                ) : (
                  <span className="font-normal">
                    {key === 'extraFields' ? (
                      <span
                        className={`whitespace-pre-wrap ${getFontSize(valueStr)}`}
                      >
                        {valueStr}
                      </span>
                    ) : (
                      <>
                        <span className="text-accent-base">值: </span>
                        <span
                          className={`whitespace-pre-wrap ${getFontSize(valueStr)}`}
                        >
                          {valueStr}
                        </span>
                      </>
                    )}
                  </span>
                )}
              </div>
            </div>
          );
        })}
    </div>
  );
};

interface PromptRowProps {
  name: string;
  template: string;
  onEdit: (name: string, template: string) => void;
}

const PromptRow: React.FC<PromptRowProps> = ({ name, template, onEdit }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="bg-zinc-700 rounded p-4 mb-4">
      <div className="flex flex-col">
        <div className="flex justify-between items-center mb-2">
          <div className="text-accent-base font-medium">{name}</div>
          <div className="flex gap-2">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-400 hover:text-accent-base"
            >
              {isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
            </button>
            <button
              onClick={() => onEdit(name, template)}
              className="text-gray-400 hover:text-accent-base"
            >
              <SquarePen size={20} />
            </button>
          </div>
        </div>
        <div
          className={`text-white mt-1 text-sm ${isExpanded ? 'whitespace-pre-wrap' : 'truncate'}`}
        >
          {template}
        </div>
      </div>
    </div>
  );
};

const Index: React.FC = () => {
  const [appData, setAppData] = useState<AppData | null>(null);
  const [activeTab, setActiveTab] = useState('config');
  const [selectedPromptName, setSelectedPromptName] = useState<string>('');
  const [selectedPromptTemplate, setSelectedPromptTemplate] =
    useState<string>('');
  const [isEditPromptDialogOpen, setIsEditPromptDialogOpen] = useState(false);
  const { pipeline, getClient } = useUserContext();

  const fetchAppData = useCallback(async () => {
    try {
      const client = await getClient();
      if (!client) {
        throw new Error('Failed to get authenticated client');
      }

      const response: WrappedSettingsResponse = await client.system.settings();

      if (response && response.results) {
        setAppData({
          config: response.results.config,
          prompts: response.results.prompts.results,
        });
      } else {
        throw new Error('Unexpected response structure');
      }
    } catch (err) {
      console.error('Error fetching app data:', err);
    }
  }, [getClient]);

  useEffect(() => {
    if (pipeline?.deploymentUrl) {
      fetchAppData();
    }
  }, [pipeline?.deploymentUrl, fetchAppData]);

  const { config = {}, prompts = [] } = appData || {};

  const handleEditPrompt = (name: string, template: string) => {
    setSelectedPromptName(name);
    setSelectedPromptTemplate(template);
    setIsEditPromptDialogOpen(true);
  };

  const handleSaveSuccess = () => {
    if (pipeline?.deploymentUrl) {
      fetchAppData();
    }
  };

  // Function to generate the alphabet list
  const generateAlphabetList = () => {
    const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
    return alphabet.map((letter) => (
      <button
        key={letter}
        className="px-2 py-1 text-white hover:text-accent-base"
        style={{ letterSpacing: '0.8em' }} // Adjust the spacing as needed
        onClick={() =>
          document
            .getElementById(letter)
            ?.scrollIntoView({ behavior: 'smooth' })
        }
      >
        {letter}
      </button>
    ));
  };

  // Function to render sections alphabetically
  const renderAlphabeticalConfigSections = () => {
    const groupedSections: Record<string, JSX.Element[]> = {};

    // First, sort the config keys alphabetically
    const sortedKeys = Object.keys(config).sort((a, b) =>
      a.toLowerCase().localeCompare(b.toLowerCase())
    );

    sortedKeys.forEach((key) => {
      const sectionKey = key.charAt(0).toUpperCase();
      if (!groupedSections[sectionKey]) {
        groupedSections[sectionKey] = [];
      }

      groupedSections[sectionKey].push(
        <div key={key} className="mb-4">
          <h3 className="text-2xl font-bold mb-2 text-white">
            {getConfigKeyTranslation(key)}
          </h3>
          {renderNestedConfig(config[key])}
        </div>
      );
    });

    // Sort the section keys alphabetically
    return Object.keys(groupedSections)
      .sort((a, b) => a.localeCompare(b))
      .map((letter) => (
        <div key={letter} id={letter} className="mb-6">
          <h4 className="text-xl font-bold text-accent-base pb-2">{letter}</h4>
          <div>{groupedSections[letter]}</div>
        </div>
      ));
  };

  return (
    <Layout pageTitle="系统设置">
      <main className="w-full flex flex-col min-h-screen container bg-zinc-900 text-white p-4 mt-4">
        <div className="mx-auto w-full max-w-5xl mb-12 mt-4">
          <div className="mt-8">
            <div className="flex justify-between items-center mb-4">
              <div className="flex justify-center ml-auto">
                <Button
                  className={`px-4 py-2 rounded mr-2`}
                  color={activeTab === 'config' ? 'filled' : 'secondary'}
                  shape={activeTab === 'config' ? 'default' : 'outline'}
                  onClick={() => setActiveTab('config')}
                >
                  配置
                </Button>
                <Button
                  className={`px-4 py-2 rounded`}
                  color={activeTab === 'prompts' ? 'filled' : 'secondary'}
                  shape={activeTab === 'prompts' ? 'default' : 'outline'}
                  onClick={() => setActiveTab('prompts')}
                >
                  提示词
                </Button>
              </div>
            </div>
            <div className="bg-zinc-800 p-4 rounded">
              {activeTab === 'config' && (
                <>
                  <h4 className="text-xl font-bold text-white pb-2">配置</h4>
                  <div className="flex mb-4">{generateAlphabetList()}</div>
                  <div className="overflow-x-auto max-w-full">
                    <div className="w-full bg-zinc-800">
                      {Object.keys(config).length > 0 ? (
                        renderAlphabeticalConfigSections()
                      ) : (
                        <div className="px-4 py-2 text-white text-center">
                          暂无有效的配置数据
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}
              {activeTab === 'prompts' && (
                <>
                  <h4 className="text-xl font-bold text-white pb-2">提示词</h4>
                  <div className="overflow-x-auto max-w-full">
                    <div className="w-full bg-zinc-800">
                      {prompts && prompts.length > 0 ? (
                        <div className="flex flex-col">
                          {prompts.map((prompt) => (
                            <PromptRow
                              key={prompt.name}
                              name={prompt.name}
                              template={prompt.template}
                              onEdit={handleEditPrompt}
                            />
                          ))}
                        </div>
                      ) : (
                        <div className="px-4 py-2 text-white text-center">
                          暂无可用的提示词
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </main>
      <EditPromptDialog
        open={isEditPromptDialogOpen}
        onClose={() => setIsEditPromptDialogOpen(false)}
        promptName={selectedPromptName}
        promptTemplate={selectedPromptTemplate}
        onSaveSuccess={handleSaveSuccess}
      />
    </Layout>
  );
};

export default Index;
