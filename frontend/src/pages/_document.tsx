import { Html, Head, Main, NextScript } from 'next/document';

export default function Document() {
  return (
    <Html lang="zh-CN">
      <Head>
        {/* 页面标题和描述 */}
        <title>中证数智-企业知识库管理平台</title>
        <meta name="description" content="中证数智企业级智能知识库管理平台，高效管理和检索企业知识资产" />
        <meta name="keywords" content="知识库,企业管理,智能检索,中证数智,知识管理" />
        
        {/* Favicon 配置 */}
        <link rel="icon" type="image/png" href="/images/logo.png" />
        <link rel="icon" type="image/x-icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/images/logo.png" />
        <link rel="shortcut icon" href="/images/logo.png" />
        
        {/* 移动端配置 */}
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#B91C1C" />
        
        {/* Open Graph 元数据 */}
        <meta property="og:title" content="中证数智-企业知识库管理平台" />
        <meta property="og:description" content="企业级智能知识库管理平台，高效管理和检索企业知识资产" />
        <meta property="og:type" content="website" />
        <meta property="og:image" content="/images/logo.png" />

        {/* Twitter Card 元数据 */}
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="中证数智-企业知识库管理平台" />
        <meta name="twitter:description" content="企业级智能知识库管理平台，高效管理和检索企业知识资产" />
        <meta name="twitter:image" content="/images/logo.png" />
        
        {/* 字体预加载 */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
